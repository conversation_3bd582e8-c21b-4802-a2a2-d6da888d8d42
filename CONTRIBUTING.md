# Contributing to Stack Craft Platform 🤝

Thank you for your interest in contributing to Stack Craft Platform! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

Before contributing, ensure you have:
- [Clarinet](https://docs.hiro.so/clarinet) installed
- [Node.js](https://nodejs.org/) (v16 or higher)
- [Git](https://git-scm.com/)
- Basic knowledge of Clarity smart contracts

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/Stack_Craft_Platform.git
   cd Stack_Craft_Platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Verify setup**
   ```bash
   clarinet check
   npm test
   ```

## 📋 Contribution Guidelines

### Code Style

- **Clarity Contracts**: Follow Clarity best practices
- **TypeScript**: Use consistent formatting with Prettier
- **Comments**: Write clear, descriptive comments
- **Naming**: Use descriptive variable and function names

### Commit Messages

Use conventional commit format:
```
type(scope): description

Examples:
feat(contracts): add new reward calculation logic
fix(admin): resolve permission check issue
docs(readme): update installation instructions
test(stack_craft): add comprehensive test coverage
```

### Pull Request Process

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Write clean, well-documented code
   - Add tests for new functionality
   - Ensure all tests pass

3. **Test thoroughly**
   ```bash
   clarinet check
   npm test
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**
   - Use a descriptive title
   - Provide detailed description
   - Reference any related issues

## 🧪 Testing Requirements

### Smart Contract Tests
- All new contracts must have comprehensive tests
- Existing tests must continue to pass
- Test both success and failure scenarios

### Test Coverage
- Aim for high test coverage
- Test edge cases and error conditions
- Include integration tests where appropriate

## 🐛 Bug Reports

When reporting bugs, please include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (Clarinet version, OS, etc.)
- Relevant logs or error messages

## 💡 Feature Requests

For new features:
- Check existing issues first
- Provide clear use case description
- Explain the benefit to users
- Consider implementation complexity

## 🔍 Code Review Process

All contributions go through code review:
- Maintainers will review your PR
- Address feedback promptly
- Be open to suggestions and improvements
- Ensure CI checks pass

## 📚 Documentation

When contributing:
- Update relevant documentation
- Add inline code comments
- Update README if needed
- Include examples for new features

## 🏷️ Issue Labels

We use these labels to organize issues:
- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements to docs
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed

## 🤔 Questions?

If you have questions:
- Check existing issues and discussions
- Create a new issue with the `question` label
- Join our community discussions

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to Stack Craft Platform! 🚀
