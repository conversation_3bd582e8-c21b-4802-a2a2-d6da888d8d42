# Stack Craft Platform 🚀

A decentralized platform built on the Stacks blockchain that rewards users with STX tokens for various development activities including website creation, design work, backend development, and other contributions.

## 🌟 Features

- **Activity Logging**: Track and reward user activities automatically
- **STX Rewards**: Earn STX tokens for completed tasks
- **Admin Controls**: Configurable reward rates and contract management
- **User Profiles**: View activity history and total earnings
- **Modular Architecture**: Clean, maintainable smart contract design

## 📋 Smart Contracts

### Core Contracts

1. **`stack_craft.clar`** - Main contract containing core functionality
   - Activity logging and STX reward distribution
   - User activity tracking
   - Admin functions for contract management

2. **`type_and_constant.clar`** - Shared constants and helper functions
   - Error codes and admin configuration
   - User data structures
   - Helper functions for data management

### Supporting Contracts

3. **`activity_log.clar`** - Activity logging delegation
4. **`admin.clar`** - Administrative utility functions
5. **`config.clar`** - Reward rate configuration management
6. **`user_storage.clar`** - User data access and retrieval

## 🎯 Activity Types & Rewards

| Activity Type | Default Reward | Description |
|---------------|----------------|-------------|
| Website | 100 STX | Complete website development |
| Design | 50 STX | UI/UX design work |
| Backend | 120 STX | Backend development tasks |
| Others | 30 STX | Miscellaneous contributions |

*Reward rates are configurable by admin*

## 🛠️ Technology Stack

- **Blockchain**: Stacks (Bitcoin Layer 2)
- **Smart Contract Language**: Clarity
- **Development Framework**: Clarinet
- **Testing**: Vitest with Clarinet SDK
- **Language**: TypeScript for tests

## 🚀 Getting Started

### Prerequisites

- [Clarinet](https://docs.hiro.so/clarinet) installed
- [Node.js](https://nodejs.org/) (for running tests)
- [Git](https://git-scm.com/)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Taiwoahmad/Stack_Craft_Platform.git
   cd Stack_Craft_Platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Check contracts**
   ```bash
   clarinet check
   ```

4. **Run tests**
   ```bash
   npm test
   ```

## 📖 Usage

### For Users

1. **Log Activity**
   ```clarity
   (contract-call? .stack_craft log-activity "website")
   ```

2. **Check Profile**
   ```clarity
   (contract-call? .stack_craft get-user-summary 'ST1PQHQKV0RJXZFY1DGX8MNSNYVE3VGZJSRTPGZGM)
   ```

### For Admins

1. **Update Reward Rates**
   ```clarity
   (contract-call? .stack_craft set-reward-rate "website" u200)
   ```

2. **Fund Contract**
   ```clarity
   (contract-call? .stack_craft fund-contract)
   ```

## 🧪 Testing

The project includes comprehensive test suites for all contracts:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:report

# Watch mode for development
npm run test:watch
```

## 🏗️ Development

### Project Structure

```
Stack_Craft_Platform/
├── contracts/           # Smart contracts
├── tests/              # Test files
├── settings/           # Network configurations
├── .vscode/           # VS Code settings
├── Clarinet.toml      # Project configuration
└── package.json       # Dependencies
```

### Contract Architecture

The platform uses a modular architecture where:
- `stack_craft.clar` serves as the main contract
- Supporting contracts delegate functionality to the main contract
- Shared constants and helpers are centralized in `type_and_constant.clar`

## 🔧 Configuration

### Network Settings

- **Devnet**: Local development environment
- **Testnet**: Stacks testnet deployment
- **Mainnet**: Production deployment

### Clarinet Configuration

All contracts are configured in `Clarinet.toml` with:
- Clarity version 3
- Epoch 3.1
- Proper dependency management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built on the [Stacks](https://www.stacks.co/) blockchain
- Developed with [Clarinet](https://docs.hiro.so/clarinet)
- Testing framework by [Hiro](https://www.hiro.so/)

## 📞 Support

For support and questions:
- Create an issue in this repository
- Check the [Stacks documentation](https://docs.stacks.co/)
- Visit the [Clarinet documentation](https://docs.hiro.so/clarinet)

---

**Built with ❤️ on Stacks blockchain**
