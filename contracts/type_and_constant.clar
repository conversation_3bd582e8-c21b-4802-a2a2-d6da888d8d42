;; Defines core constants, types, and global variables

;; Error constants
(define-constant ERR-UNAUTHORIZED u100)
(define-constant ERR-INVALID-ACTIVITY u101)

;; Admin address (set at deploy time)
(define-constant ADMIN tx-sender)

;; Track total rewards paid out
(define-data-var total-rewards-paid uint u0)

;; Reward rate defaults (modifiable by admin)
(define-data-var reward-website uint u100)     ;; 100 STX
(define-data-var reward-design uint u50)       ;; 50 STX
(define-data-var reward-backend uint u120)     ;; 120 STX
(define-data-var reward-others uint u30)       ;; 30 STX

;; Activity record map
(define-map user-activities
  principal
  {
    websites-created: uint,
    designs-done: uint,
    backend-built: uint,
    others: uint,
    stx-earned: uint
  }
)

;; Helper function to initialize user if not exists
(define-private (initialize-user (user principal))
  (let ((exists (map-get? user-activities user)))
    (match exists
      entry (ok entry)
      (begin
        (map-set user-activities user {
          websites-created: u0,
          designs-done: u0,
          backend-built: u0,
          others: u0,
          stx-earned: u0
        })
        (ok {
          websites-created: u0,
          designs-done: u0,
          backend-built: u0,
          others: u0,
          stx-earned: u0
        })
      )
    )
  )
)

;; Helper function to get user entry with defaults
(define-private (get-user-entry (user principal))
  (match (map-get? user-activities user)
    entry entry
    {
      websites-created: u0,
      designs-done: u0,
      backend-built: u0,
      others: u0,
      stx-earned: u0
    }
  )
)

;; Public getter functions for constants
(define-read-only (get-admin)
  ADMIN
)

(define-read-only (get-err-unauthorized)
  ERR-UNAUTHORIZED
)

(define-read-only (get-err-invalid-activity)
  ERR-INVALID-ACTIVITY
)