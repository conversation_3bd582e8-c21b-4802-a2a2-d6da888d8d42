;; Admin-only utility functions

(define-public (fund-contract (amount uint))
  (begin
    ;; Only admin can deposit STX
    (asserts! (is-eq tx-sender ADMIN) (err ERR-UNAUTHORIZED))
    ;; Transfer from sender into this contract
    (stx-transfer? amount tx-sender (as-contract tx-sender))
  )
)

(define-read-only (get-contract-balance)
  (stx-get-balance (as-contract tx-sender))
)

;; Future admin functions placeholder
;; (define-public (pause-contract) ...)
;; (define-public (resume-contract) ...)

;; End of admin.clar