;; Admin-only utility functions
;; Uses constants from type_and_constant contract

(define-public (fund-contract (amount uint))
  (begin
    ;; Only admin can deposit STX
    (asserts! (is-eq tx-sender (contract-call? .type_and_constant get-admin)) (err (contract-call? .type_and_constant get-err-unauthorized)))
    ;; Transfer from sender into this contract
    (stx-transfer? amount tx-sender (as-contract tx-sender))
  )
)

(define-read-only (get-contract-balance)
  (stx-get-balance (as-contract tx-sender))
)

;; Future admin functions placeholder
;; (define-public (pause-contract) ...)
;; (define-public (resume-contract) ...)