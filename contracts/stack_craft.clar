(define-constant admin tx-sender)

(define-data-var total-rewards-paid uint u0)

;; User data to track activity
(define-map user-activities
  principal
  {
    websites-created: uint,
    designs-done: uint,
    backend-built: uint,
    others: uint,
    stx-earned: uint
  }
)

;; Define reward rates per activity (can be changed later by admin)
(define-data-var reward-website uint u100) ;; 100 STX per website
(define-data-var reward-design uint u50)   ;; 50 STX per design
(define-data-var reward-backend uint u120) ;; 120 STX per backend
(define-data-var reward-others uint u30)   ;; 30 STX for other contributions

;; Allow admin to update reward amounts
(define-public (set-reward-rate (activity-type (string-ascii 20)) (new-rate uint))
  (begin
    (asserts! (is-eq tx-sender admin) (err u997))
    (if (is-eq activity-type "website")
      (begin (var-set reward-website new-rate) (ok true))
      (if (is-eq activity-type "design")
        (begin (var-set reward-design new-rate) (ok true))
        (if (is-eq activity-type "backend")
          (begin (var-set reward-backend new-rate) (ok true))
          (if (is-eq activity-type "others")
            (begin (var-set reward-others new-rate) (ok true))
            (err u998)
          )
        )
      )
    )
  )
)

;; Allow admin to deposit STX into the contract
(define-public (fund-contract)
  (begin
    (asserts! (is-eq tx-sender admin) (err u996))
    (stx-transfer? (stx-get-balance tx-sender) tx-sender (as-contract tx-sender))
  )
)

;; Core function: log user activity and reward STX
(define-public (log-activity (activity-type (string-ascii 20)))
  (let
    (
      (sender tx-sender)
      (user-entry (match (map-get? user-activities sender)
                    entry entry
                    {websites-created: u0, designs-done: u0, backend-built: u0, others: u0, stx-earned: u0}))
    )
    (if (is-eq activity-type "website")
      (let ((reward (var-get reward-website)))
        (begin
          (map-set user-activities sender {
            websites-created: (+ (get websites-created user-entry) u1),
            designs-done: (get designs-done user-entry),
            backend-built: (get backend-built user-entry),
            others: (get others user-entry),
            stx-earned: (+ (get stx-earned user-entry) reward)
          })
          (try! (stx-transfer? reward (as-contract tx-sender) sender))
          (var-set total-rewards-paid (+ (var-get total-rewards-paid) reward))
          (ok reward)
        )
      )
      (if (is-eq activity-type "design")
        (let ((reward (var-get reward-design)))
          (begin
            (map-set user-activities sender {
              websites-created: (get websites-created user-entry),
              designs-done: (+ (get designs-done user-entry) u1),
              backend-built: (get backend-built user-entry),
              others: (get others user-entry),
              stx-earned: (+ (get stx-earned user-entry) reward)
            })
            (try! (stx-transfer? reward (as-contract tx-sender) sender))
            (var-set total-rewards-paid (+ (var-get total-rewards-paid) reward))
            (ok reward)
          )
        )
        (if (is-eq activity-type "backend")
          (let ((reward (var-get reward-backend)))
            (begin
              (map-set user-activities sender {
                websites-created: (get websites-created user-entry),
                designs-done: (get designs-done user-entry),
                backend-built: (+ (get backend-built user-entry) u1),
                others: (get others user-entry),
                stx-earned: (+ (get stx-earned user-entry) reward)
              })
              (try! (stx-transfer? reward (as-contract tx-sender) sender))
              (var-set total-rewards-paid (+ (var-get total-rewards-paid) reward))
              (ok reward)
            )
          )
          (if (is-eq activity-type "others")
            (let ((reward (var-get reward-others)))
              (begin
                (map-set user-activities sender {
                  websites-created: (get websites-created user-entry),
                  designs-done: (get designs-done user-entry),
                  backend-built: (get backend-built user-entry),
                  others: (+ (get others user-entry) u1),
                  stx-earned: (+ (get stx-earned user-entry) reward)
                })
                (try! (stx-transfer? reward (as-contract tx-sender) sender))
                (var-set total-rewards-paid (+ (var-get total-rewards-paid) reward))
                (ok reward)
              )
            )
            (err u999)
          )
        )
      )
    )
  )
)

;; View user profile
(define-read-only (get-user-summary (user principal))
  (map-get? user-activities user)
)

;; View total rewards paid from contract
(define-read-only (get-total-rewards)
  (var-get total-rewards-paid)
)
