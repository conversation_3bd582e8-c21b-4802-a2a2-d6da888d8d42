;; Functions to configure reward rates by admin

(define-public (set-reward-website (new-rate uint))
  (begin
    (asserts! (is-eq tx-sender <PERSON>MIN) (err ERR-UNAUTHORIZED))
    (var-set reward-website new-rate)
    (ok new-rate)
  )
)

(define-public (set-reward-design (new-rate uint))
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err ERR-UNAUTH<PERSON>IZ<PERSON>))
    (var-set reward-design new-rate)
    (ok new-rate)
  )
)

(define-public (set-reward-backend (new-rate uint))
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err ERR-UNAUTHORIZED))
    (var-set reward-backend new-rate)
    (ok new-rate)
  )
)

(define-public (set-reward-others (new-rate uint))
  (begin
    (asserts! (is-eq tx-sender ADMIN) (err ERR-UNAUTHORIZED))
    (var-set reward-others new-rate)
    (ok new-rate)
  )
)

;; End of config.clar