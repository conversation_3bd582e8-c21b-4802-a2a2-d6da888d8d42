;; Functions to configure reward rates by admin
;; This contract delegates to the main stack_craft contract

(define-public (set-reward-website (new-rate uint))
  (begin
    (contract-call? .stack_craft set-reward-rate "website" new-rate)
  )
)

(define-public (set-reward-design (new-rate uint))
  (begin
    (contract-call? .stack_craft set-reward-rate "design" new-rate)
  )
)

(define-public (set-reward-backend (new-rate uint))
  (begin
    (contract-call? .stack_craft set-reward-rate "backend" new-rate)
  )
)

(define-public (set-reward-others (new-rate uint))
  (begin
    (contract-call? .stack_craft set-reward-rate "others" new-rate)
  )
)