[project]
name = 'stack_craft'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.activity_log]
path = 'contracts/activity_log.clar'
clarity_version = 3
epoch = 3.1

[contracts.admin]
path = 'contracts/admin.clar'
clarity_version = 3
epoch = 3.1

[contracts.config]
path = 'contracts/config.clar'
clarity_version = 3
epoch = 3.1

[contracts.stack_craft]
path = 'contracts/stack_craft.clar'
clarity_version = 3
epoch = 3.1

[contracts.type_and_constant]
path = 'contracts/type_and_constant.clar'
clarity_version = 3
epoch = 3.1

[contracts.user_storage]
path = 'contracts/user_storage.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false

[repl.remote_data]
enabled = false
api_url = 'https://api.hiro.so'
