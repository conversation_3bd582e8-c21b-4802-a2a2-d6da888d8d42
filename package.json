{"name": "stack_craft-tests", "version": "1.0.0", "description": "Run unit tests on this project.", "type": "module", "private": true, "scripts": {"test": "vitest run", "test:report": "vitest run -- --coverage --costs", "test:watch": "chokidar \"tests/**/*.ts\" \"contracts/**/*.clar\" -c \"npm run test:report\""}, "author": "", "license": "ISC", "dependencies": {"@hirosystems/clarinet-sdk": "^2.14.0", "@stacks/transactions": "^6.12.0", "chokidar-cli": "^3.0.0", "typescript": "^5.6.0", "vite": "^6.1.0", "vitest": "^3.0.0", "vitest-environment-clarinet": "^2.3.0"}}